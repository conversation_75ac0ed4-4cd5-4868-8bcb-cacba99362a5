/**
 * 日志中间件
 * 记录每个请求的详细信息
 */

import morgan from "morgan";
import { Request, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";
import { createModuleLogger } from "../utils/logger";

const logger = createModuleLogger("HTTP");

/**
 * 生成请求ID中间件
 */
export function requestIdMiddleware(
  req: any,
  res: any,
  next: NextFunction
): void {
  // 生成唯一的请求ID
  req.requestId = uuidv4();

  // 将请求ID添加到响应头
  res.setHeader("X-Request-ID", req.requestId);

  next();
}

/**
 * 请求信息提取中间件
 */
export function requestInfoMiddleware(
  req: any,
  _res: any,
  next: NextFunction
): void {
  // 提取客户端IP
  req.clientIp =
    req.ip ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    req.connection?.socket?.remoteAddress ||
    "unknown";

  // 提取用户代理
  req.userAgent = req.get("User-Agent") || "unknown";

  next();
}

/**
 * 请求开始时间中间件
 */
export function requestTimingMiddleware(
  req: any,
  res: any,
  next: NextFunction
): void {
  // 记录请求开始时间
  req.startTime = Date.now();

  // 监听响应完成事件
  res.on("finish", () => {
    const duration = Date.now() - req.startTime;

    // 记录请求日志
    logger.request(
      req.method,
      req.originalUrl,
      res.statusCode,
      duration,
      req.requestId
    );

    // 性能监控：如果请求时间过长，记录警告
    if (duration > 1000) {
      logger.warn(
        `检测到慢请求: ${req.method} ${req.originalUrl} 耗时 ${duration}ms`,
        {
          requestId: req.requestId,
          method: req.method,
          url: req.originalUrl,
          statusCode: res.statusCode,
          duration,
          userAgent: req.userAgent,
          clientIp: req.clientIp,
        }
      );
    }
  });

  next();
}

/**
 * 自定义 Morgan 日志格式
 */
const customMorganFormat = (tokens: any, req: any, res: any) => {
  const requestId = req.requestId || "unknown";
  const method = tokens.method(req, res);
  const url = tokens.url(req, res);
  const status = tokens.status(req, res);
  const responseTime = tokens["response-time"](req, res);
  const contentLength = tokens.res(req, res, "content-length") || "0";
  const userAgent = tokens["user-agent"](req, res) || "unknown";
  const remoteAddr = tokens["remote-addr"](req, res);

  return `[${requestId}] ${remoteAddr} "${method} ${url}" ${status} ${contentLength} ${responseTime}ms "${userAgent}"`;
};

/**
 * Morgan 中间件配置
 */
export const morganMiddleware: any = morgan(customMorganFormat, {
  // 跳过健康检查等路由的日志
  skip: (req: Request) => {
    return req.url === "/health";
  },

  // 自定义日志流
  stream: {
    write: (message: string) => {
      // 移除末尾的换行符并记录到我们的日志系统
      logger.info(message.trim());
    },
  },
});

/**
 * 开发环境详细日志中间件
 */
export function developmentLoggerMiddleware(
  req: any,
  res: any,
  next: NextFunction
): void {
  if (process.env.NODE_ENV === "development") {
    logger.debug("收到请求", {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl,
      headers: req.headers,
      query: req.query,
      body: req.body,
      clientIp: req.clientIp,
      userAgent: req.userAgent,
    });

    // 监听响应完成事件，记录响应信息
    res.on("finish", () => {
      logger.debug("响应已发送", {
        requestId: req.requestId,
        statusCode: res.statusCode,
        headers: res.getHeaders(),
      });
    });
  }

  next();
}

/**
 * 安全日志中间件
 * 记录可疑的请求活动
 */
export function securityLoggerMiddleware(
  req: any,
  res: any,
  next: NextFunction
): void {
  const suspiciousPatterns = [
    /\.\./, // 路径遍历
    /<script/i, // XSS 尝试
    /union.*select/i, // SQL 注入尝试
    /exec\(/i, // 代码执行尝试
  ];

  const url = req.originalUrl;
  const userAgent = req.userAgent || "";
  const body = JSON.stringify(req.body || {});

  // 检查可疑模式
  const isSuspicious = suspiciousPatterns.some(
    (pattern) =>
      pattern.test(url) || pattern.test(userAgent) || pattern.test(body)
  );

  if (isSuspicious) {
    logger.warn("检测到可疑请求", {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl,
      clientIp: req.clientIp,
      userAgent: req.userAgent,
      body: req.body,
      headers: req.headers,
    });
  }

  // 记录失败的认证尝试
  res.on("finish", () => {
    if (res.statusCode === 401 || res.statusCode === 403) {
      logger.warn("认证/授权失败", {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        clientIp: req.clientIp,
        userAgent: req.userAgent,
      });
    }
  });

  next();
}

/**
 * 组合日志中间件
 * 将所有日志相关的中间件组合在一起
 */
export function createLoggerMiddleware(): Array<
  (req: any, res: any, next: NextFunction) => void
> {
  return [
    requestIdMiddleware,
    requestInfoMiddleware,
    requestTimingMiddleware,
    morganMiddleware,
    developmentLoggerMiddleware,
    securityLoggerMiddleware,
  ];
}
